import 'dart:io';
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';

import '../main.dart';
import '../utillites/common_function.dart';

// class FCMService extends GetxService {
//   FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
//   FlutterLocalNotificationsPlugin();
//
//   final GetStorage box = GetStorage();
//
//   AndroidNotificationChannel channel = const AndroidNotificationChannel(
//     'high_importance_channel',
//     'High Importance Notifications',
//     description: 'This channel is used for important notifications.',
//     importance: Importance.max,
//     showBadge: true,
//     playSound: true,
//   );
//
//   bool _initialized = false;
//
//   Future<void> init() async {
//     if (!_initialized) {
//       await _initializeLocalNotifications();
//       await _initializeFCM();
//       _initialized = true;
//     }
//   }
//
//   Future<void> _initializeLocalNotifications() async {
//     const AndroidInitializationSettings initializationSettingsAndroid =
//     AndroidInitializationSettings("@mipmap/ic_launcher");
//
//     const DarwinInitializationSettings initializationSettingsIOS =
//     DarwinInitializationSettings(
//       requestSoundPermission: true,
//       requestBadgePermission: true,
//       requestAlertPermission: true,
//       defaultPresentAlert: true,
//       defaultPresentSound: true,
//       defaultPresentBadge: true,
//     );
//
//     const InitializationSettings initializationSettings =
//     InitializationSettings(
//       android: initializationSettingsAndroid,
//       iOS: initializationSettingsIOS,
//     );
//
//     await flutterLocalNotificationsPlugin.initialize(
//       initializationSettings,
//       onDidReceiveNotificationResponse: selectNotification,
//     );
//
//     if (Platform.isAndroid) {
//       await flutterLocalNotificationsPlugin
//           .resolvePlatformSpecificImplementation<
//           AndroidFlutterLocalNotificationsPlugin>()
//           ?.createNotificationChannel(channel);
//     }
//
//     if (Platform.isIOS) {
//       await flutterLocalNotificationsPlugin
//           .resolvePlatformSpecificImplementation<
//           IOSFlutterLocalNotificationsPlugin>()
//           ?.requestPermissions(alert: true, badge: true, sound: true);
//
//       // Ensure iOS notifications have sound, badge, and alert in foreground
//       await FirebaseMessaging.instance
//           .setForegroundNotificationPresentationOptions(
//         alert: true,
//         badge: true,
//         sound: true,
//       );
//     }
//   }
//
//   Future<void> _initializeFCM() async {
//     if (Platform.isIOS) {
//       await FirebaseMessaging.instance.requestPermission(
//           badge: true, sound: true, alert: true);
//     }
//
//     FirebaseMessaging.instance
//         .getInitialMessage()
//         .then((RemoteMessage? message) {
//       if (message != null) {
//         _handleMessage(message);
//       }
//     });
//
//     FirebaseMessaging.onMessage.listen((RemoteMessage message) {
//       // Show a local notification on iOS and Android
//       print("message ===> ${message.data}");
//       if(Platform.isAndroid) {
//         _showNotification(message);
//       }
//     });
//
//     FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage? message) {
//       print("message ===> ${message?.data}");
//       if (message != null) {
//         _handleMessage(message);
//         // Clear badge when user opens the app from notification
//         // _clearBadgeCount();
//       }
//     });
//   }
//
//   Future<void> _showNotification(RemoteMessage message) async {
//     // int currentBadgeCount = _incrementBadgeCount();
//
//     final AndroidNotificationDetails androidNotificationDetails =
//     AndroidNotificationDetails(
//       channel.id,
//       channel.name,
//       channelDescription: channel.description,
//       icon: '@mipmap/ic_launcher',
//       channelShowBadge: true,
//       // number: currentBadgeCount,
//     );
//
//     final DarwinNotificationDetails iOSNotificationDetails =
//     DarwinNotificationDetails(
//       presentAlert: true,
//       presentBadge: true,
//       presentSound: true,
//       sound: 'default',
//       // badgeNumber: currentBadgeCount,
//     );
//
//     final NotificationDetails notificationDetails = NotificationDetails(
//       android: androidNotificationDetails,
//       iOS: iOSNotificationDetails,
//     );
//
//     await flutterLocalNotificationsPlugin.show(
//       0,
//       message.notification?.title,
//       message.notification?.body,
//       notificationDetails,
//       payload: message.data.toString(),
//     );
//   }
//
//   Future<void> selectNotification(
//       NotificationResponse? notificationResponse) async {
//     if (notificationResponse?.payload != null) {
//       // _clearBadgeCount();
//     }
//   }
//
//   Future<void> _handleMessage(RemoteMessage message) async {
//   }
//
//   // int _incrementBadgeCount() {
//   //   int currentCount = box.read('badgecount') ?? 0;
//   //   int newCount = currentCount + 1;
//   //   box.write('badgecount', newCount);
//   //   return newCount;
//   // }
//   //
//   // void _clearBadgeCount() {
//   //   box.write('badgecount', 0);
//   //
//   // }
//   //
//   // int getBadgeCount() {
//   //   return box.read('badgecount') ?? 0;
//   // }
//   //
//   // void setBadgeCount(int count) {
//   //   box.write('badgecount', count);
//   // }
//   //
//   // void clearBadgeOnAppOpen() {
//   //   _clearBadgeCount();
//   // }
// }

bool isNullEmptyOrFalse(dynamic o) {
  if (o is Map<String, dynamic> || o is List<dynamic>) {
    return o == null || o.length == 0;
  }
  return o == null || false == o || "" == o;
}

class FCMService extends GetxService {
  bool _initialized = false;

  Future<void> init() async {
    if (!_initialized) {
      await initAwesomeNotifications();
      await _initializeFCM();
      _initialized = true;
    }
  }

  Future<void> initAwesomeNotifications() async {
    await AwesomeNotifications().initialize(
      'resource://mipmap/ic_launcher',
      [
        NotificationChannel(
          channelKey: 'high_importance_channel',
          channelName: 'High Importance Notifications',
          channelDescription: 'This channel is used for important notifications.',
          enableVibration: true,
          defaultPrivacy: NotificationPrivacy.Public,
          playSound: true,
          criticalAlerts: true,
          importance: NotificationImportance.High,
          channelShowBadge: true,
          locked: true,
        )
      ],
    );

    // await AwesomeNotifications().isNotificationAllowed().then((
    //     isAllowed) async {
    //   if (!isAllowed) {
    //     await AwesomeNotifications().requestPermissionToSendNotifications();
    //   }
    // });
    bool isAllowed = await AwesomeNotifications().isNotificationAllowed();


    if (!isAllowed && !(box.read('notification_permission_requested') ?? false)) {
      // Mark that we have asked once
      box.write('notification_permission_requested', true);

      await AwesomeNotifications().requestPermissionToSendNotifications();
    }

    await AwesomeNotifications().setListeners(
        onActionReceivedMethod: onActionReceivedMethod,
        onNotificationCreatedMethod: onNotificationCreatedMethod,
        onNotificationDisplayedMethod: onNotificationDisplayedMethod
    );
  }


  @pragma('vm:entry-point')
  static Future<void> onActionReceivedMethod(
      ReceivedAction receivedAction) async {
    Map<String, dynamic>? payload = receivedAction.payload;
    print("payload ===> $payload");
      CommonFunction.getNotificationOnTap(receivedAction.payload ?? {});
  }

  @pragma('vm:entry-point')
  static Future<void> onNotificationCreatedMethod(
      ReceivedNotification receivedNotification) async {
  }

  @pragma('vm:entry-point')
  static Future<void> onNotificationDisplayedMethod(
      ReceivedNotification receivedNotification) async {
  }

  Future<void> _initializeFCM() async {
    if (Platform.isIOS) {
      await FirebaseMessaging.instance.requestPermission(
          badge: true, sound: true, alert: true);
    }

    FirebaseMessaging.instance
        .getInitialMessage()
        .then((RemoteMessage? message) {
      if (message != null) {
        _handleMessage(message);
        if(Platform.isAndroid) {
          CommonFunction.getNotificationOnTap(message.data ?? {});
        }
      }
    });

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      // Show a local notification on iOS and Android
      print("message ===> ${message.data}");
      if(Platform.isAndroid) {
        Future.delayed(const Duration(seconds: 1), () {
          showNotification(message);
        });
      }
    });

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage? message) {
      print("message ===> ${message?.data}");
      if (message != null) {
        Future.delayed(const Duration(seconds: 1), () {
          _handleMessage(message);
        });
      }
    });
  }

  Future<void> showNotification(RemoteMessage message) async {
    Map<String, dynamic> data = message.data;
    final int notificationId = DateTime
        .now()
        .millisecondsSinceEpoch % 100000;


    await AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: notificationId,
        channelKey: 'high_importance_channel',
        title: data['title'] ?? message.notification?.title ??
            'New Notification',
        body: data['body'] ?? message.notification?.body ?? '',
        payload: data.map((key, value) => MapEntry(key, value.toString())),
        notificationLayout: NotificationLayout.Default,
        category: NotificationCategory.Message,
        displayOnForeground: true,
        displayOnBackground: true,
        wakeUpScreen: true,
        fullScreenIntent: true,
        criticalAlert: true,
        autoDismissible: true,
      ),
    );
  }

  Future<void> _handleMessage(RemoteMessage message) async {
  }

  @pragma('vm:entry-point')
  Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {

    await AwesomeNotifications().initialize(
      'resource://mipmap/ic_launcher',
      [
        NotificationChannel(
          channelKey: 'high_importance_channel',
          channelName: 'High Importance Notifications',
          channelDescription: 'This channel is used for important notifications.',
          enableVibration: true,
          defaultPrivacy: NotificationPrivacy.Public,
          playSound: true,
          criticalAlerts: true,
          importance: NotificationImportance.High,
          channelShowBadge: true,
        )
      ],
    );

    FCMService fcmService = FCMService();
    await fcmService.showNotification(message);
  }
}