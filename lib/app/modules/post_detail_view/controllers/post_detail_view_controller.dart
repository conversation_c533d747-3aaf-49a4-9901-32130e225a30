
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/utillites/app_theme.dart';

import '../../../../constants/constant.dart';
import '../../../../models/app_comment_model.dart';
import '../../../../models/app_post_model.dart';
import '../../../../models/post_like_model.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/common_function.dart';
import '../../../../utillites/current_user.dart';

class PostDetailViewController extends GetxController with WidgetsBindingObserver {

  ApiManager apiManager = ApiManager();
  RxBool isLoading = false.obs;
  RxBool isPostNotFound = false.obs;
  Rx<Post> getPostDetailData = Post().obs;
  var args = Get.arguments;
  RxInt postId = (-1).obs;
  RxInt projectId = (-1).obs;
  RxString postSlug = ('').obs;
  RxInt index = (-1).obs;
  RxBool isFollowing = false.obs;
  RxBool isShare = false.obs;
  RxString source = "".obs;
  RxString selectedReason = "".obs;
  RxList<PostLikeData> postLikeList = <PostLikeData>[].obs;
  RxBool hasMoreData = true.obs;
  TextEditingController commentController = TextEditingController();
  FocusNode commentFocusNode =FocusNode();
  RxList<Comment> commentDataList = <Comment>[].obs;
  RxBool isShowMessage = false.obs;
  RxList repostData = [
    "I just don't Like it",
    "Scam, fraud or spam",
    "False information",
  ].obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    // Register this controller as a lifecycle observer
    WidgetsBinding.instance.addObserver(this);

    if(args != null && args["postID"] != null) {
      postId.value = args["postID"];
    }
    if(args != null && args["projectId"] != null) {
      projectId.value = args["projectId"];
    }
    if(args != null && args["postSlug"] != null) {
      postSlug.value = args["postSlug"];
    }
    if(args != null && args["index"] != null) {
      index.value = args["index"];
      print("index: ${index.value}");
    }
    if(args != null && args["source"] != null) {
      source.value = args["source"];
      print("source: ${source.value}");
    }
    print("onInit postId: ${postId.value}");
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    print("AppLifecycleState changed: ${state.toString()}");
    print("isShare1: ${isShare.value}");

    if(isShare.value) {
      if(state == AppLifecycleState.paused) {
        isShowMessage.value = true;
      }
      if (state == AppLifecycleState.resumed) {
        isShare.value = false;
        if(isShowMessage.value) {
          isShowMessage.value = false;
          CommonFunction.showCustomSnackbar(
              message: "The post has been shared successfully");
        }
      }
      return;
    }
  }

  @override
  void onReady() {
    print("onReady postId: ${postId.value}");
    callApiForGetOnePost(context: Get.context!);
  }

  Future<void> callApiForCommentProject({required BuildContext context,String? postId}) {
    // FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost =
    ApiModel("/post-comments/post/$postId", APIType.POST);

    return apiManager.callApi(
      getPost,
      params: {"comment": commentController.value.text.trim()},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");

            isLoading.value = false;

            Comment newComment;

            if (response['data'] != null) {
              newComment = Comment.fromJson(response['data']);
            } else {
              newComment = Comment(
                id: DateTime.now().millisecondsSinceEpoch, // Temporary ID
                comment: commentController.value.text.trim(),
                createdAt: DateTime.now(),
                user: CurrentUser.user,
              );
            }

            List<Comment> updatedComments = [newComment, ...commentDataList];
            commentDataList.value = updatedComments;
          }
        } catch (error) {
          log("Error in callApiForCommentProject: $error");
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForGetCommentProject({required BuildContext context,String? postId}) {
    // FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost =
    ApiModel("/post-comments/post/$postId", APIType.GET);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CommentResponse commentResponse = CommentResponse.fromJson(response);
            commentDataList.value = commentResponse.data.data;
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForLikeProject({required BuildContext context,String? postId}) {
    FocusScope.of(context).unfocus();
    // isLoading.value = true;
    final ApiModel getPost =
    ApiModel("/post-likes/post/$postId", APIType.POST);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            // isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForGetPostLike({required BuildContext context,required int postId}) async {
    isLoading.value = true;
    FocusScope.of(context).unfocus();
    String? lastCreatedAt = postLikeList.isNotEmpty
        ? postLikeList.last.createdAt?.toIso8601String()
        : null;

    try {
      await apiManager.callApi(
        ApiModel("/post-likes/post/$postId", APIType.GET),
        params: {"limit": 20, if (lastCreatedAt != null) "createdAt": lastCreatedAt},
        successCallback: (response, message) async {
          if (response['status'] == 'success') {
            PostLike postLike = PostLike.fromJson(response);
            if ((postLike.data?.data?.length ?? 0) < 20) {
              hasMoreData.value = false;
            }
            if ((postLike.data?.data ?? []).isNotEmpty) {
              postLikeList.addAll(postLike.data?.data ?? []);
              postLikeList.refresh();
            }
          }
          isLoading.value = false;
        },
        failureCallback: (message, statusCode) {
          hasMoreData.value = false;
          isLoading.value = false;

        },
      );
    } catch (error) {
      hasMoreData.value = false;
      isLoading.value = false;

    }
  }

  callApiForBookMarkProject(
      {required BuildContext context, String? postId}) {
    FocusScope.of(context).unfocus();
    final ApiModel getPost = ApiModel("/bookmarks/post/$postId", APIType.POST);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            // postDataList[index].isBookMarked = response["post"]["isBookMarked"] == "0" ? false : true;
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }


  callApiForReportPost(
      {required BuildContext context, required String postId}) {
    FocusScope.of(context).unfocus();
    // isLoading.value = true;
    final ApiModel updatePost =
    ApiModel("/reported-posts/post/$postId", APIType.POST);
    log("selected ==> ${selectedReason.value}");
    return apiManager.callApi(
      updatePost,
      params: {"reason": selectedReason.value},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            // Navigator.pop(context);
            CommonFunction.showCustomSnackbar(message: response['message']);
          }
        } catch (error) {
          log("error ==> $error");
        }
      },
      failureCallback: (message, statusCode) {
        CommonFunction.showCustomSnackbar(message: "You’ve already reported this post.",isError: true,backgroundColor: AppTheme.red);
        log("error: $message");
      },
    );
  }

  Future<void> callApiForFollowUser(
      {required BuildContext context,required int? userId}) {
    FocusScope.of(context).unfocus();
    final ApiModel updatePost = ApiModel("/friends/follow/user/$userId", APIType.POST);
    isFollowing.value = true;
    return apiManager.callApi(
      updatePost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            isFollowing.value = true;
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
          }
        } catch (error) {
          isFollowing.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isFollowing.value = false;
      },
    );
  }

  Future<void> callApiForUnFollowUser(
      {required BuildContext context,required int? userId}) {
    FocusScope.of(context).unfocus();
    final ApiModel updatePost = ApiModel("/friends/user/$userId", APIType.DELETE);
    isFollowing.value = false;
    return apiManager.callApi(
      updatePost,
      params: {"unfollow": 1},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            isFollowing.value = false;
            CommonFunction.showCustomSnackbar(
              message: response['message'],
            );
          }
        } catch (error) {
          isFollowing.value = true;
        }
      },
      failureCallback: (message, statusCode) {
        isFollowing.value = true;
      },
    );
  }
  callApiForGetOnePost({required BuildContext context}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    var parameter = postSlug.value.isNotEmpty ? postSlug.value : postId.value.toString();
    final ApiModel getPost = ApiModel("/posts/$parameter", APIType.GET);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("get one post response === $response");
            if(response['data'] == null) {
              isPostNotFound.value = true;
              isLoading.value = false;
              return;
            }
            if(response["data"]["User"]["isFollowed"] == "1") {
              isFollowing.value = true;
            } else {
              isFollowing.value = false;
            }
            getPostDetailData.value = Post.fromJson(response["data"]);
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  @override
  void onClose() {
    // Remove this controller as a lifecycle observer to prevent memory leaks
    WidgetsBinding.instance.removeObserver(this);
    commentController.dispose();
    commentFocusNode.dispose();
    super.onClose();
  }
}
