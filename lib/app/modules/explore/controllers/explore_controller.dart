import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:incenti_ai/models/post_like_model.dart';
import 'package:incenti_ai/utillites/common_function.dart';
import 'package:incenti_ai/utillites/current_user.dart';
import '../../../../constants/api.dart';
import '../../../../constants/constant.dart';
import '../../../../main.dart';
import '../../../../models/app_comment_model.dart';
import '../../../../models/app_post_model.dart';
import '../../../../models/story_model.dart';
import '../../../../services/api_manager.dart';
import '../../../../utillites/app_theme.dart';
import '../../bottom_bar/controllers/bottom_bar_controller.dart';
import '../../story_editor/controllers/story_editor_controller.dart';

class ExploreController extends GetxController with WidgetsBindingObserver {
  DateTime? _backgroundTime;
  RxList<Story> storyDataList = <Story>[].obs;
  RxList<Story> currentStories = <Story>[].obs;
  RxList<OneStory> userStoryList = <OneStory>[].obs;
  TextEditingController commentController = TextEditingController();
  FocusNode commentFocusNode =FocusNode();
  RxBool isLoading = false.obs;
  RxBool isExploreLoading = false.obs;
  ApiManager apiManager = ApiManager();
  RxList<Post> postDataList = <Post>[].obs;
  RxList<PostLikeData> postLikeList = <PostLikeData>[].obs;
  RxList<Comment> commentDataList = <Comment>[].obs;
  int limit = 10;
  RxInt page = 1.obs;
  RxInt count = (-1).obs;
  RxBool hasMoreData = true.obs;
  RxBool isUploading = false.obs;
  RxBool isShare = false.obs;
  RxBool isShowMessage = false.obs;
  Timer? uploadCheckTimer;
  RxString selectedReason = "".obs;
  RxList repostData = [
    "I just don't Like it",
    "Scam, fraud or spam",
    "False information",
  ].obs;

  bool _previousUploadState = false;

  @override
  void onInit() {
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
    callApiForGetCurrentStory();
    callApiForExplorePost(context: Get.context!);

    ever(isUploading, (bool uploadingState) {
      _handleUploadStateChange(uploadingState);
    });
  }

  @override
  void onClose() {
    stopUploadStatusCheck();
    WidgetsBinding.instance.removeObserver(this);
    super.onClose();
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    print("state ==> $state");
    if(isShare.value) {
      if(state == AppLifecycleState.paused) {
        isShowMessage.value = true;
      }
      if (state == AppLifecycleState.resumed) {
        isShare.value = false;
        if(isShowMessage.value) {
          isShowMessage.value = false;
          CommonFunction.showCustomSnackbar(
              message: "The post has been shared successfully");
        }
      }
      return;
    }
    if (state == AppLifecycleState.paused) {
      _backgroundTime = DateTime.now();
    } else if (state == AppLifecycleState.resumed) {
      if (_backgroundTime != null) {
        final now = DateTime.now();
        final difference = now.difference(_backgroundTime!);
        if (difference.inMinutes >= 3) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Get.find<BottomBarController>().scrollController.animateTo(0, duration: Duration(seconds: 1), curve: Curves.easeInOut).then((value) =>  pullRefresh(),);
          });
        }
      }
    }
  }


  void _handleUploadStateChange(bool currentUploadState) {
    if (_previousUploadState == true && currentUploadState == false) {
      log('Story upload completed! Refreshing story data...');
      callApiForGetCurrentStory();
    }
    _previousUploadState = currentUploadState;
  }

  void startUploadStatusCheck() {
    checkUploadStatus();
    uploadCheckTimer = Timer.periodic(Duration(seconds: 1), (_) {
      checkUploadStatus();
    });

    Timer(Duration(minutes: 5), () {
      stopUploadStatusCheck();
      log('Timer automatically stopped after 5 minutes');
    });
  }

  void stopUploadStatusCheck() {
    uploadCheckTimer?.cancel();
    uploadCheckTimer = null;
    isUploading.value = false;
  }

  Future<void> checkUploadStatus() async {
    bool currentStatus = await StoryBackgroundUploadService.backgroundService.isRunning();
    isUploading.value = currentStatus;
  }

  pullRefresh() {
    page.value = 1;
    hasMoreData.value = true;
    postDataList.value = [];
    callApiForExplorePost(context: Get.context!);
    callApiForGetCurrentStory();
    CurrentUser.getMe();
  }


  callApiForReportPost(
      {required BuildContext context, required String postId}) {
    FocusScope.of(context).unfocus();
    final ApiModel updatePost =
    ApiModel("/reported-posts/post/$postId", APIType.POST);
    log("selected ==> ${selectedReason.value}");
    return apiManager.callApi(
      updatePost,
      params: {"reason": selectedReason.value},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CommonFunction.showCustomSnackbar(message: response['message']);
            postDataList.clear();
            page.value = 1;
            hasMoreData.value = true;
            callApiForExplorePost(context: context);
          }
        } catch (error) {
        }
      },
      failureCallback: (message, statusCode) {
        CommonFunction.showCustomSnackbar(message: "You've already reported this post.",isError: true,backgroundColor: AppTheme.red);
      },
    );
  }

  callApiForBlockUser(
      {required BuildContext context, required int userId}) {
    FocusScope.of(context).unfocus();
    final ApiModel block =
    ApiModel("/users/block/$userId", APIType.PATCH);
    return apiManager.callApi(
      block,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            Get.back();
            CommonFunction.showCustomSnackbar(message: response['message']);
            postDataList.clear();
            page.value = 1;
            hasMoreData.value = true;
            callApiForExplorePost(context: context);
          }
        } catch (error) {
        }
      },
      failureCallback: (message, statusCode) {
        CommonFunction.showCustomSnackbar(message: "You've already reported this post.",isError: true,backgroundColor: AppTheme.red);
      },
    );
  }

  callApiForExplorePost({required BuildContext context}) async {
    // Prevent multiple simultaneous API calls
    if (isExploreLoading.value) return;

    FocusScope.of(context).unfocus();
    isExploreLoading.value = true;
    // String? lastCreatedAt = postDataList.isNotEmpty
    //     ? postDataList.last.createdAt?.toIso8601String()
    //     : null;

    try {
      await apiManager.callApi(
        APIS.post.getAllExplorePost,
        params: {"limit": limit, "page": page.value},
        successCallback: (response, message) async {
          if (response['status'] == 'success') {
            PostResponse postResponse = PostResponse.fromJson(response);
            if (postResponse.data.data.length < limit) {
              hasMoreData.value = false;
            }

            if (postResponse.data.data.isNotEmpty) {
              postDataList.addAll(postResponse.data.data);

              if(box.read("newPost") != null) {
                postDataList.insert(0, box.read("newPost"));
                box.remove("newPost");
              }
              postDataList.refresh();
            }
            page.value++;
          }
        },
        failureCallback: (message, statusCode) {
          hasMoreData.value = false;
        },
      );
    } catch (error) {
      hasMoreData.value = false;
    } finally {
      isExploreLoading.value = false;
    }
  }

  callApiForBookMarkProject({required BuildContext context,String? postId, required int index}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    postDataList[index].isBookMarked?.value = !(postDataList[index].isBookMarked?.value ?? false);
    final ApiModel getPost =
    ApiModel("/bookmarks/post/$postId", APIType.POST);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  callApiForLikeProject({required BuildContext context,String? postId, required int index}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    postDataList[index].isLiked?.value = !(postDataList[index].isLiked?.value ?? false);
    if(postDataList[index].isLiked?.value == true){
      postDataList[index].likesCount?.value ++;
    } else {
      postDataList[index].likesCount?.value --;
    }
    final ApiModel getPost =
    ApiModel("/post-likes/post/$postId", APIType.POST);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForCommentProject({required BuildContext context,String? postId,required int index}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost =
    ApiModel("/post-comments/post/$postId", APIType.POST);

    return apiManager.callApi(
      getPost,
      params: {"comment": commentController.value.text.trim()},
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");

            isLoading.value = false;

            Comment newComment;

            if (response['data'] != null) {
              newComment = Comment.fromJson(response['data']);
            } else {
              newComment = Comment(
                id: DateTime.now().millisecondsSinceEpoch, // Temporary ID
                comment: commentController.value.text.trim(),
                createdAt: DateTime.now(),
                user: CurrentUser.user,
              );
            }

            List<Comment> updatedComments = [newComment, ...commentDataList];
            commentDataList.value = updatedComments;

            postDataList[index].commentsCount?.value = commentDataList.length;
          }
        } catch (error) {
          log("Error in callApiForCommentProject: $error");
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForGetCommentProject({required BuildContext context,String? postId,required int index}) {
    FocusScope.of(context).unfocus();
    isLoading.value = true;
    final ApiModel getPost =
    ApiModel("/post-comments/post/$postId", APIType.GET);

    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            log("response === $response");
            CommentResponse commentResponse = CommentResponse.fromJson(response);
            commentDataList.value = commentResponse.data.data;
            postDataList[index].commentsCount?.value = commentDataList.length;
            isLoading.value = false;
          }
        } catch (error) {
          isLoading.value = false;
        }
      },
      failureCallback: (message, statusCode) {
        isLoading.value = false;
      },
    );
  }

  Future<void> callApiForGetCurrentStory(){
    final ApiModel getPost =
    ApiModel("/stories/", APIType.GET);
    return apiManager.callApi(
      getPost,
      successCallback: (response, message) async {
        try {
          if (response['status'] == 'success') {
            AllStory storyData = AllStory.fromJson(response);
            currentStories.value = storyData.data ?? [];

            if (currentStories.isNotEmpty) {
              int currentUserStoriesCount = (storyDataList.isNotEmpty && storyDataList[0].stories != null)
                  ? storyDataList[0].stories!.length
                  : 0;
              int apiStoriesCount = (currentStories[0].stories ?? []).length;

              log('Current user stories count: $currentUserStoriesCount');
              log('API stories count: $apiStoriesCount');

              if (apiStoriesCount < currentUserStoriesCount) {
                log('API stories count is less than local. Polling again in 2 seconds...');
                Future.delayed(Duration(seconds: 2), () {
                  callApiForGetCurrentStory();
                });
              } else {
                log('Stories synchronized successfully!');
                storyDataList.value = currentStories;
              }
            } else {
              storyDataList.value = currentStories;
            }
          }
        } catch (error) {
          log('Error in callApiForGetCurrentStory: $error');
        }
      },
      failureCallback: (message, statusCode) {
        log('API call failed: $message');
      },
    );
  }

  callApiForGetPostLike({required BuildContext context,required int postId}) async {
    isLoading.value = true;
    FocusScope.of(context).unfocus();
    String? lastCreatedAt = postLikeList.isNotEmpty
        ? postLikeList.last.createdAt?.toIso8601String()
        : null;

    try {
      await apiManager.callApi(
        ApiModel("/post-likes/post/$postId", APIType.GET),
        params: {"limit": 20, if (lastCreatedAt != null) "createdAt": lastCreatedAt},
        successCallback: (response, message) async {
          if (response['status'] == 'success') {
            PostLike postLike = PostLike.fromJson(response);
            if ((postLike.data?.data?.length ?? 0) < 20) {
              hasMoreData.value = false;
            }
            if ((postLike.data?.data ?? []).isNotEmpty) {
              postLikeList.addAll(postLike.data?.data ?? []);
              postLikeList.refresh();
            }
          }
          isLoading.value = false;
        },
        failureCallback: (message, statusCode) {
          hasMoreData.value = false;
          isLoading.value = false;
        },
      );
    } catch (error) {
      hasMoreData.value = false;
      isLoading.value = false;
    }
  }

}